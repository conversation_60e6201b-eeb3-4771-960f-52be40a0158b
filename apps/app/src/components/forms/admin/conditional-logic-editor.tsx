"use client"

import React from "react"
import { <PERSON>, <PERSON>Off, Plus, Trash2 } from "lucide-react"

import type {
  ConditionalLogicGroup,
  ConditionalLogicRule,
  ConditionalOperator,
  ShowConditions
} from "@/types/conditional-logic"
import { getAvailableOperators, getOperatorDisplayName } from "@/types/conditional-logic"
import { But<PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Switch } from "@nextui-org/switch"

interface ConditionalLogicEditorProps {
  showConditions?: ShowConditions | null
  onChange: (conditions: ShowConditions | null) => void
  availableQuestions: Array<{
    id: string
    title: string
    type: string
    order: number
  }>
  currentQuestionOrder: number
}

export const ConditionalLogicEditor: React.FC<ConditionalLogicEditorProps> = ({
  showConditions,
  onChange,
  availableQuestions,
  currentQuestionOrder,
}) => {
  const [isEnabled, setIsEnabled] = React.useState(!!showConditions)

  // Filter questions that come before the current question
  const previousQuestions = availableQuestions.filter(q => q.order < currentQuestionOrder)

  const handleToggle = (enabled: boolean) => {
    setIsEnabled(enabled)
    if (!enabled) {
      onChange(null)
    } else {
      onChange({
        groups: [{
          id: crypto.randomUUID(),
          rules: [],
          logic: "AND"
        }],
        groupLogic: "AND"
      })
    }
  }

  const addGroup = () => {
    if (!showConditions) return

    const newGroup: ConditionalLogicGroup = {
      id: crypto.randomUUID(),
      rules: [],
      logic: "AND"
    }

    onChange({
      ...showConditions,
      groups: [...(showConditions.groups || []), newGroup]
    })
  }

  const removeGroup = (groupId: string) => {
    if (!showConditions || !showConditions.groups) return

    onChange({
      ...showConditions,
      groups: showConditions.groups.filter(g => g.id !== groupId)
    })
  }

  const updateGroup = (groupId: string, updates: Partial<ConditionalLogicGroup>) => {
    if (!showConditions || !showConditions.groups) return

    onChange({
      ...showConditions,
      groups: showConditions.groups.map(g =>
        g.id === groupId ? { ...g, ...updates } : g
      )
    })
  }

  const addRule = (groupId: string) => {
    if (!showConditions || !showConditions.groups || previousQuestions.length === 0) return

    const newRule: ConditionalLogicRule = {
      id: crypto.randomUUID(),
      questionId: previousQuestions[0].id,
      operator: "equals",
      value: ""
    }

    const group = showConditions.groups.find(g => g.id === groupId)
    if (!group) return

    updateGroup(groupId, {
      rules: [...(group.rules || []), newRule]
    })
  }

  const removeRule = (groupId: string, ruleId: string) => {
    if (!showConditions || !showConditions.groups) return

    const group = showConditions.groups.find(g => g.id === groupId)
    if (!group || !group.rules) return

    updateGroup(groupId, {
      rules: group.rules.filter(r => r.id !== ruleId)
    })
  }

  const updateRule = (groupId: string, ruleId: string, updates: Partial<ConditionalLogicRule>) => {
    if (!showConditions || !showConditions.groups) return

    const group = showConditions.groups.find(g => g.id === groupId)
    if (!group || !group.rules) return

    updateGroup(groupId, {
      rules: group.rules.map(r =>
        r.id === ruleId ? { ...r, ...updates } : r
      )
    })
  }

  if (previousQuestions.length === 0) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <EyeOff className="size-8 text-default-400 mx-auto mb-2" />
          <p className="text-default-500">
            La logique conditionnelle n'est disponible que pour les questions qui ont des questions précédentes.
          </p>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <Eye className="size-5" />
          <h3 className="text-lg font-semibold">Logique conditionnelle</h3>
        </div>
        <Switch
          isSelected={isEnabled}
          onValueChange={handleToggle}
          size="sm"
        >
          {isEnabled ? "Activée" : "Désactivée"}
        </Switch>
      </CardHeader>

      {isEnabled && (
        <CardBody className="space-y-4">
          <p className="text-sm text-default-600">
            Cette question sera affichée uniquement si les conditions suivantes sont remplies :
          </p>

          {showConditions && (
            <div className="space-y-4">
              {/* Group Logic Selector */}
              {showConditions.groups?.length > 1 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Combiner les groupes avec :</span>
                  <Select
                    size="sm"
                    selectedKeys={[showConditions.groupLogic]}
                    onSelectionChange={(keys) => {
                      const logic = Array.from(keys)[0] as "AND" | "OR"
                      onChange({ ...showConditions, groupLogic: logic })
                    }}
                    className="w-24"
                    aria-label="Logique de combinaison des groupes"
                  >
                    <SelectItem key="AND" value="AND">ET</SelectItem>
                    <SelectItem key="OR" value="OR">OU</SelectItem>
                  </Select>
                </div>
              )}

              {/* Groups */}
              {showConditions.groups?.map((group, groupIndex) => (
                <Card key={group.id} className="border border-default-200">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center w-full">
                      <Chip size="sm" variant="flat">
                        Groupe {groupIndex + 1}
                      </Chip>
                      <div className="flex items-center gap-2">
                        {group.rules.length > 1 && (
                          <Select
                            size="sm"
                            selectedKeys={[group.logic]}
                            onSelectionChange={(keys) => {
                              const logic = Array.from(keys)[0] as "AND" | "OR"
                              updateGroup(group.id, { logic })
                            }}
                            className="w-20"
                            aria-label={`Logique du groupe ${groupIndex + 1}`}
                          >
                            <SelectItem key="AND" value="AND">ET</SelectItem>
                            <SelectItem key="OR" value="OR">OU</SelectItem>
                          </Select>
                        )}
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color="danger"
                          onPress={() => removeGroup(group.id)}
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardBody className="space-y-3">
                    {/* Rules */}
                    {group.rules.map((rule, ruleIndex) => (
                      <RuleEditor
                        key={rule.id}
                        rule={rule}
                        ruleIndex={ruleIndex}
                        previousQuestions={previousQuestions}
                        onUpdate={(updates) => updateRule(group.id, rule.id, updates)}
                        onRemove={() => removeRule(group.id, rule.id)}
                      />
                    ))}

                    <Button
                      size="sm"
                      variant="flat"
                      startContent={<Plus className="size-4" />}
                      onPress={() => addRule(group.id)}
                    >
                      Ajouter une règle
                    </Button>
                  </CardBody>
                </Card>
              ))}

              <Button
                size="sm"
                variant="bordered"
                startContent={<Plus className="size-4" />}
                onPress={addGroup}
              >
                Ajouter un groupe
              </Button>
            </div>
          )}
        </CardBody>
      )}
    </Card>
  )
}

interface RuleEditorProps {
  rule: ConditionalLogicRule
  ruleIndex: number
  previousQuestions: Array<{
    id: string
    title: string
    type: string
    options?: Array<{ id: string; label: string; value: string }> | string | null
  }>
  onUpdate: (updates: Partial<ConditionalLogicRule>) => void
  onRemove: () => void
}

const RuleEditor: React.FC<RuleEditorProps> = ({
  rule,
  ruleIndex,
  previousQuestions,
  onUpdate,
  onRemove,
}) => {
  const selectedQuestion = previousQuestions.find(q => q.id === rule.questionId)
  const availableOperators = selectedQuestion ? getAvailableOperators(selectedQuestion.type) : []

  return (
    <div className="flex items-center gap-2 p-3 bg-default-50 rounded">
      <Chip size="sm" variant="flat" color="primary">
        {ruleIndex + 1}
      </Chip>

      {/* Question Selector */}
      <Select
        size="sm"
        placeholder="Question"
        selectedKeys={rule.questionId ? [rule.questionId] : []}
        onSelectionChange={(keys) => {
          const questionId = Array.from(keys)[0] as string
          onUpdate({ questionId })
        }}
        className="flex-1"
        aria-label={`Question pour la règle ${ruleIndex + 1}`}
      >
        {previousQuestions.map((question) => (
          <SelectItem key={question.id} value={question.id}>
            {question.title}
          </SelectItem>
        ))}
      </Select>

      {/* Operator Selector */}
      <Select
        size="sm"
        placeholder="Opérateur"
        selectedKeys={rule.operator ? [rule.operator] : []}
        onSelectionChange={(keys) => {
          const operator = Array.from(keys)[0] as ConditionalOperator
          onUpdate({ operator })
        }}
        className="w-48"
        aria-label={`Opérateur pour la règle ${ruleIndex + 1}`}
      >
        {availableOperators.map((operator) => (
          <SelectItem key={operator} value={operator}>
            {getOperatorDisplayName(operator)}
          </SelectItem>
        ))}
      </Select>

      {/* Value Input */}
      {rule.operator && !["is_empty", "is_not_empty"].includes(rule.operator) && (
        <ConditionValueInput
          rule={rule}
          selectedQuestion={selectedQuestion}
          onUpdate={onUpdate}
          ruleIndex={ruleIndex}
        />
      )}

      <Button
        isIconOnly
        size="sm"
        variant="light"
        color="danger"
        onPress={onRemove}
      >
        <Trash2 className="size-4" />
      </Button>
    </div>
  )
}

// Smart condition value input component
interface ConditionValueInputProps {
  rule: ConditionalLogicRule
  selectedQuestion?: {
    id: string
    title: string
    type: string
    options?: Array<{ id: string; label: string; value: string }> | string | null
  }
  onUpdate: (updates: Partial<ConditionalLogicRule>) => void
  ruleIndex: number
}

const ConditionValueInput: React.FC<ConditionValueInputProps> = ({
  rule,
  selectedQuestion,
  onUpdate,
  ruleIndex,
}) => {
  // Helper function to parse options from JSON string or return array
  const parseOptions = (options: Array<{ id: string; label: string; value: string }> | string | null | undefined): Array<{ id: string; label: string; value: string }> => {
    if (!options) return []
    if (typeof options === 'string') {
      try {
        return JSON.parse(options) as Array<{ id: string; label: string; value: string }>
      } catch (error) {
        console.warn('Failed to parse question options:', error)
        return []
      }
    }
    return options
  }

  if (!selectedQuestion) {
    return (
      <Input
        size="sm"
        placeholder="Valeur"
        value={String(rule.value || "")}
        onValueChange={(value) => onUpdate({ value })}
        className="flex-1"
        aria-label={`Valeur de comparaison pour la règle ${ruleIndex + 1}`}
      />
    )
  }

  // For YES_NO questions, show boolean options
  if (selectedQuestion.type === "YES_NO") {
    return (
      <Select
        size="sm"
        placeholder="Sélectionner"
        selectedKeys={rule.value !== undefined ? [String(rule.value)] : []}
        onSelectionChange={(keys) => {
          const value = Array.from(keys)[0] as string
          // Store as boolean for YES_NO questions
          onUpdate({ value: value === "true" })
        }}
        className="flex-1"
        aria-label={`Valeur de comparaison pour la règle ${ruleIndex + 1}`}
      >
        <SelectItem key="true" value="true">Oui</SelectItem>
        <SelectItem key="false" value="false">Non</SelectItem>
      </Select>
    )
  }

  // For SINGLE_CHOICE and MULTIPLE_CHOICE questions, show available options
  if (selectedQuestion.type === "SINGLE_CHOICE" || selectedQuestion.type === "MULTIPLE_CHOICE") {
    const options = parseOptions(selectedQuestion.options)

    if (options.length > 0) {
      return (
        <Select
          size="sm"
          placeholder="Sélectionner"
          selectedKeys={rule.value !== undefined ? [String(rule.value)] : []}
          onSelectionChange={(keys) => {
            const value = Array.from(keys)[0] as string
            onUpdate({ value })
          }}
          className="flex-1"
          aria-label={`Valeur de comparaison pour la règle ${ruleIndex + 1}`}
        >
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </Select>
      )
    }
  }

  // For NUMBER and RATING questions, use number input
  if (selectedQuestion.type === "NUMBER" || selectedQuestion.type === "RATING") {
    return (
      <Input
        size="sm"
        type="number"
        placeholder="Nombre"
        value={String(rule.value || "")}
        onValueChange={(value) => {
          const numValue = parseFloat(value)
          onUpdate({ value: isNaN(numValue) ? undefined : numValue })
        }}
        className="flex-1"
        aria-label={`Valeur de comparaison pour la règle ${ruleIndex + 1}`}
      />
    )
  }

  // Default to text input for other question types
  return (
    <Input
      size="sm"
      placeholder="Valeur"
      value={String(rule.value || "")}
      onValueChange={(value) => onUpdate({ value })}
      className="flex-1"
      aria-label={`Valeur de comparaison pour la règle ${ruleIndex + 1}`}
    />
  )
}
