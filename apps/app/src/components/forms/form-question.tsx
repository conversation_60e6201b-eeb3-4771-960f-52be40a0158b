"use client"

import React from "react"
import { motion } from "framer-motion"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { Checkbox, CheckboxGroup } from "@nextui-org/checkbox"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { Radio, RadioGroup } from "@nextui-org/radio"
import { Slider } from "@nextui-org/slider"
import { QuestionType } from "@prisma/client"

interface QuestionOption {
  id: string
  label: string
  value: string
}

interface FormQuestionData {
  id: string
  title: string
  description?: string | null
  type: QuestionType
  isRequired: boolean
  minLength?: number | null
  maxLength?: number | null
  minValue?: number | null
  maxValue?: number | null
  options?: QuestionOption[] | string | null // Can be array, JSON string, or null
}

interface FormResponse {
  questionId: string
  textValue?: string
  numberValue?: number
  booleanValue?: boolean
  dateValue?: Date
  selectedOptions?: string[]
}

interface FormQuestionProps {
  question: FormQuestionData
  value?: FormResponse
  onChange: (response: Partial<FormResponse>) => void
  error?: string
}

export const FormQuestion: React.FC<FormQuestionProps> = ({
  question,
  value,
  onChange,
  error,
}) => {
  // Helper function to parse options from JSON string or return array
  const parseOptions = (options: QuestionOption[] | string | null | undefined): QuestionOption[] => {
    if (!options) return []
    if (typeof options === 'string') {
      try {
        return JSON.parse(options) as QuestionOption[]
      } catch (error) {
        console.warn('Failed to parse question options:', error)
        return []
      }
    }
    return options
  }

  // Helper function to handle null values
  const safeNumber = (value: number | null | undefined): number | undefined => {
    return value === null ? undefined : value
  }
  const renderQuestionInput = () => {
    switch (question.type) {
      case QuestionType.TEXT_SHORT:
        return (
          <Input
            placeholder="Votre réponse..."
            value={value?.textValue || ""}
            onChange={(e) => onChange({ textValue: e.target.value })}
            isInvalid={!!error}
            errorMessage={error}
            maxLength={safeNumber(question.maxLength)}
            className="w-full"
          />
        )

      case QuestionType.TEXT_LONG:
        return (
          <Textarea
            placeholder="Votre réponse..."
            value={value?.textValue || ""}
            onChange={(e) => onChange({ textValue: e.target.value })}
            isInvalid={!!error}
            errorMessage={error}
            maxLength={safeNumber(question.maxLength)}
            minRows={4}
            className="w-full"
          />
        )

      case QuestionType.EMAIL:
        return (
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={value?.textValue || ""}
            onChange={(e) => onChange({ textValue: e.target.value })}
            isInvalid={!!error}
            errorMessage={error}
            className="w-full"
          />
        )

      case QuestionType.NUMBER:
        return (
          <Input
            type="number"
            placeholder="Entrez un nombre"
            value={value?.numberValue?.toString() || ""}
            onChange={(e) => {
              const num = parseFloat(e.target.value)
              onChange({ numberValue: isNaN(num) ? undefined : num })
            }}
            isInvalid={!!error}
            errorMessage={error}
            min={safeNumber(question.minValue)}
            max={safeNumber(question.maxValue)}
            className="w-full"
          />
        )

      case QuestionType.RATING:
        const ratingMin = safeNumber(question.minValue) || 1
        const ratingMax = safeNumber(question.maxValue) || 5
        return (
          <div className="w-full">
            <Slider
              label={`Note: ${value?.numberValue || ratingMin}`}
              step={1}
              minValue={ratingMin}
              maxValue={ratingMax}
              value={value?.numberValue || ratingMin}
              onChange={(val) => onChange({ numberValue: Array.isArray(val) ? val[0] : val })}
              className="w-full"
              marks={Array.from({ length: ratingMax - ratingMin + 1 }, (_, i) => ({
                value: ratingMin + i,
                label: (ratingMin + i).toString(),
              }))}
            />
            {error && <p className="text-danger text-sm mt-2">{error}</p>}
          </div>
        )

      case QuestionType.YES_NO:
        return (
          <div className="flex gap-4">
            <Button
              variant={value?.booleanValue === true ? "solid" : "bordered"}
              color={value?.booleanValue === true ? "success" : "default"}
              onPress={() => onChange({ booleanValue: true })}
              className="flex-1"
            >
              Oui
            </Button>
            <Button
              variant={value?.booleanValue === false ? "solid" : "bordered"}
              color={value?.booleanValue === false ? "danger" : "default"}
              onPress={() => onChange({ booleanValue: false })}
              className="flex-1"
            >
              Non
            </Button>
            {error && <p className="text-danger text-sm mt-2 w-full">{error}</p>}
          </div>
        )

      case QuestionType.SINGLE_CHOICE: {
        const options = parseOptions(question.options)
        if (options.length === 0) return null
        return (
          <RadioGroup
            value={value?.selectedOptions?.[0] || ""}
            onValueChange={(val) => onChange({ selectedOptions: val ? [val] : [] })}
            isInvalid={!!error}
            errorMessage={error}
          >
            {options.map((option) => (
              <Radio key={option.id} value={option.value}>
                {option.label}
              </Radio>
            ))}
          </RadioGroup>
        )
      }

      case QuestionType.MULTIPLE_CHOICE: {
        const options = parseOptions(question.options)
        if (options.length === 0) return null
        return (
          <CheckboxGroup
            value={value?.selectedOptions || []}
            onValueChange={(vals) => onChange({ selectedOptions: vals })}
            isInvalid={!!error}
            errorMessage={error}
          >
            {options.map((option) => (
              <Checkbox key={option.id} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </CheckboxGroup>
        )
      }

      case QuestionType.DATE:
        return (
          <Input
            type="date"
            label="Sélectionnez une date"
            value={value?.dateValue ? value.dateValue.toISOString().split('T')[0] : ""}
            onChange={(e) => {
              const dateValue = e.target.value ? new Date(e.target.value) : undefined
              onChange({ dateValue })
            }}
            isInvalid={!!error}
            errorMessage={error}
            className="w-full"
          />
        )

      default:
        return (
          <div className="text-center text-default-500 p-4">
            Type de question non supporté: {question.type}
          </div>
        )
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Question Header */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium flex items-center gap-2">
          {question.title}
          {question.isRequired && (
            <span className="text-danger text-sm">*</span>
          )}
        </h3>

        {question.description && (
          <p className="text-default-600 text-sm leading-relaxed">
            {question.description}
          </p>
        )}
      </div>

      {/* Question Input */}
      <div className="space-y-2">
        {renderQuestionInput()}

        {/* Character/Value limits display */}
        {question.type === QuestionType.TEXT_SHORT || question.type === QuestionType.TEXT_LONG ? (
          <div className="flex justify-between text-xs text-default-400">
            <span>
              {value?.textValue?.length || 0}
              {safeNumber(question.maxLength) && ` / ${safeNumber(question.maxLength)}`} caractères
            </span>
            {safeNumber(question.minLength) && (
              <span>Minimum: {safeNumber(question.minLength)} caractères</span>
            )}
          </div>
        ) : null}

        {question.type === QuestionType.NUMBER && (safeNumber(question.minValue) || safeNumber(question.maxValue)) ? (
          <div className="text-xs text-default-400">
            {safeNumber(question.minValue) && safeNumber(question.maxValue)
              ? `Valeur entre ${safeNumber(question.minValue)} et ${safeNumber(question.maxValue)}`
              : safeNumber(question.minValue)
                ? `Valeur minimum: ${safeNumber(question.minValue)}`
                : `Valeur maximum: ${safeNumber(question.maxValue)}`}
          </div>
        ) : null}
      </div>
    </motion.div>
  )
}
