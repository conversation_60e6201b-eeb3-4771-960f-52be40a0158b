import { evaluateRule, evaluateShowConditions } from '../conditional-logic'
import type { ConditionalLogicRule, ShowConditions, FormResponseValue } from '../conditional-logic'

describe('Conditional Logic', () => {
  describe('evaluateRule', () => {
    it('should handle boolean equals comparison correctly', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'equals',
        value: true
      }

      const responseValue: FormResponseValue = {
        booleanValue: true
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle boolean equals comparison with string value', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'equals',
        value: 'true' // String value from admin input
      }

      const responseValue: FormResponseValue = {
        booleanValue: true
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle boolean not_equals comparison correctly', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'not_equals',
        value: 'false'
      }

      const responseValue: FormResponseValue = {
        booleanValue: true
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle single choice equals comparison', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'equals',
        value: 'option1'
      }

      const responseValue: FormResponseValue = {
        selectedOptions: ['option1']
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle text contains comparison', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'contains',
        value: 'test'
      }

      const responseValue: FormResponseValue = {
        textValue: 'This is a test message'
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle number greater_than comparison', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'greater_than',
        value: 5
      }

      const responseValue: FormResponseValue = {
        numberValue: 10
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle is_empty check', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'is_empty',
        value: ''
      }

      const responseValue: FormResponseValue = {
        textValue: ''
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })

    it('should handle is_not_empty check', () => {
      const rule: ConditionalLogicRule = {
        id: '1',
        questionId: 'q1',
        operator: 'is_not_empty',
        value: ''
      }

      const responseValue: FormResponseValue = {
        textValue: 'Some value'
      }

      expect(evaluateRule(rule, responseValue)).toBe(true)
    })
  })

  describe('evaluateShowConditions', () => {
    it('should evaluate simple AND condition correctly', () => {
      const showConditions: ShowConditions = {
        groups: [{
          id: 'group1',
          rules: [{
            id: 'rule1',
            questionId: 'q1',
            operator: 'equals',
            value: true
          }],
          logic: 'AND'
        }],
        groupLogic: 'AND'
      }

      const responses: Record<string, FormResponseValue> = {
        q1: { booleanValue: true }
      }

      expect(evaluateShowConditions(showConditions, responses)).toBe(true)
    })

    it('should evaluate complex OR condition correctly', () => {
      const showConditions: ShowConditions = {
        groups: [{
          id: 'group1',
          rules: [
            {
              id: 'rule1',
              questionId: 'q1',
              operator: 'equals',
              value: 'option1'
            },
            {
              id: 'rule2',
              questionId: 'q2',
              operator: 'equals',
              value: true
            }
          ],
          logic: 'OR'
        }],
        groupLogic: 'AND'
      }

      const responses: Record<string, FormResponseValue> = {
        q1: { selectedOptions: ['option2'] },
        q2: { booleanValue: true }
      }

      expect(evaluateShowConditions(showConditions, responses)).toBe(true)
    })

    it('should return true for empty conditions', () => {
      const showConditions: ShowConditions = {
        groups: [],
        groupLogic: 'AND'
      }

      const responses: Record<string, FormResponseValue> = {}

      expect(evaluateShowConditions(showConditions, responses)).toBe(true)
    })
  })
})
