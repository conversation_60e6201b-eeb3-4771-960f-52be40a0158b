"use client"

import React, { useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { <PERSON>ert<PERSON>ir<PERSON>, <PERSON>, ChevronLeft, ChevronRight } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import type { FormResponseValue, ShowConditions } from "@/types/conditional-logic"
import { evaluateShowConditions } from "@/types/conditional-logic"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Progress } from "@nextui-org/progress"
import { FormType, QuestionType } from "@prisma/client"

import { FormQuestion } from "./form-question"

interface FormResponse {
  questionId: string
  textValue?: string
  numberValue?: number
  booleanValue?: boolean
  dateValue?: Date
  selectedOptions?: string[]
}

interface DynamicFormProps {
  formType: FormType
  subscriptionId?: string
  onSubmissionComplete?: (submissionId: string) => void
  onCancel?: () => void
  className?: string
}

export const DynamicForm: React.FC<DynamicFormProps> = ({
  formType,
  subscriptionId,
  onSubmissionComplete,
  onCancel,
  className,
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [responses, setResponses] = useState<Record<string, FormResponse>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // Fetch the active form
  const { data: form, isLoading, error } = trpc.form.getActiveByType.useQuery(formType)

  // Submit form mutation
  const submitForm = trpc.form.submission.submit.useMutation({
    onSuccess: (submission) => {
      toast.success("Formulaire soumis avec succès!")
      onSubmissionComplete?.(submission.id)
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la soumission du formulaire")
      setIsSubmitting(false)
    },
  })

  const questions = form?.questions || []

  // Convert responses to the format expected by conditional logic
  const convertResponsesToLogicFormat = (): Record<string, FormResponseValue> => {
    const converted: Record<string, FormResponseValue> = {}
    Object.entries(responses).forEach(([questionId, response]) => {
      converted[questionId] = {
        textValue: response.textValue,
        numberValue: response.numberValue,
        booleanValue: response.booleanValue,
        dateValue: response.dateValue,
        selectedOptions: response.selectedOptions,
      }
    })
    return converted
  }

  // Filter questions based on conditional logic
  const getVisibleQuestions = (): typeof questions => {
    const logicResponses = convertResponsesToLogicFormat()

    return questions.filter(question => {
      // If no show conditions, always show the question
      if (!question.showConditions) return true

      try {
        const showConditions = typeof question.showConditions === 'string'
          ? JSON.parse(question.showConditions) as ShowConditions
          : question.showConditions as ShowConditions

        return evaluateShowConditions(showConditions, logicResponses)
      } catch (error) {
        console.warn('Error evaluating show conditions for question:', question.id, error)
        return true // Show question if there's an error in conditions
      }
    })
  }

  const visibleQuestions = getVisibleQuestions()
  const currentQuestion = visibleQuestions[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === visibleQuestions.length - 1
  const progress = visibleQuestions.length > 0 ? ((currentQuestionIndex + 1) / visibleQuestions.length) * 100 : 0

  // Validate current question
  const validateCurrentQuestion = (): boolean => {
    if (!currentQuestion) return true

    const response = responses[currentQuestion.id]
    const errors: Record<string, string> = {}

    if (currentQuestion.isRequired) {
      let hasValue = false

      switch (currentQuestion.type) {
        case QuestionType.TEXT_SHORT:
        case QuestionType.TEXT_LONG:
        case QuestionType.EMAIL:
          hasValue = !!response?.textValue?.trim()
          break
        case QuestionType.NUMBER:
        case QuestionType.RATING:
          hasValue = response?.numberValue !== undefined && response?.numberValue !== null
          break
        case QuestionType.YES_NO:
          hasValue = response?.booleanValue !== undefined
          break
        case QuestionType.DATE:
          hasValue = !!response?.dateValue
          break
        case QuestionType.SINGLE_CHOICE:
        case QuestionType.MULTIPLE_CHOICE:
          hasValue = !!response?.selectedOptions?.length
          break
      }

      if (!hasValue) {
        errors[currentQuestion.id] = "Cette question est obligatoire"
      }
    }

    // Additional validation based on question type
    if (response?.textValue) {
      if (currentQuestion.minLength && response.textValue.length < currentQuestion.minLength) {
        errors[currentQuestion.id] = `Minimum ${currentQuestion.minLength} caractères requis`
      }
      if (currentQuestion.maxLength && response.textValue.length > currentQuestion.maxLength) {
        errors[currentQuestion.id] = `Maximum ${currentQuestion.maxLength} caractères autorisés`
      }
    }

    if (response?.numberValue !== undefined) {
      if (currentQuestion.minValue && response.numberValue < currentQuestion.minValue) {
        errors[currentQuestion.id] = `Valeur minimum: ${currentQuestion.minValue}`
      }
      if (currentQuestion.maxValue && response.numberValue > currentQuestion.maxValue) {
        errors[currentQuestion.id] = `Valeur maximum: ${currentQuestion.maxValue}`
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle response change
  const handleResponseChange = (questionId: string, response: Partial<FormResponse>) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        questionId,
        ...response,
      },
    }))

    // Clear validation error for this question
    if (validationErrors[questionId]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[questionId]
        return newErrors
      })
    }
  }

  // Navigate to next question
  const handleNext = () => {
    if (!validateCurrentQuestion()) return

    if (isLastQuestion) {
      handleSubmit()
    } else {
      setCurrentQuestionIndex(prev => prev + 1)
    }
  }

  // Navigate to previous question
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  // Submit the form
  const handleSubmit = async () => {
    // Validate all visible questions
    let allValid = true
    const allErrors: Record<string, string> = {}

    visibleQuestions.forEach(question => {
      const response = responses[question.id]

      if (question.isRequired) {
        let hasValue = false

        switch (question.type) {
          case QuestionType.TEXT_SHORT:
          case QuestionType.TEXT_LONG:
          case QuestionType.EMAIL:
            hasValue = !!response?.textValue?.trim()
            break
          case QuestionType.NUMBER:
          case QuestionType.RATING:
            hasValue = response?.numberValue !== undefined && response?.numberValue !== null
            break
          case QuestionType.YES_NO:
            hasValue = response?.booleanValue !== undefined
            break
          case QuestionType.DATE:
            hasValue = !!response?.dateValue
            break
          case QuestionType.SINGLE_CHOICE:
          case QuestionType.MULTIPLE_CHOICE:
            hasValue = !!response?.selectedOptions?.length
            break
        }

        if (!hasValue) {
          allErrors[question.id] = "Cette question est obligatoire"
          allValid = false
        }
      }
    })

    if (!allValid) {
      setValidationErrors(allErrors)
      toast.error("Veuillez répondre à toutes les questions obligatoires")
      return
    }

    setIsSubmitting(true)

    const formattedResponses = Object.values(responses).map(response => ({
      questionId: response.questionId,
      textValue: response.textValue,
      numberValue: response.numberValue,
      booleanValue: response.booleanValue,
      dateValue: response.dateValue,
      selectedOptions: response.selectedOptions,
    }))

    submitForm.mutate({
      formId: form!.id,
      subscriptionId,
      responses: formattedResponses,
      ipAddress: undefined, // Will be set by server
      userAgent: navigator.userAgent,
    })
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardBody className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-default-500">Chargement du formulaire...</p>
          </div>
        </CardBody>
      </Card>
    )
  }

  if (error || !form) {
    return (
      <Card className={className}>
        <CardBody className="flex items-center justify-center p-8">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-danger mx-auto mb-4" />
            <p className="text-danger">Aucun formulaire actif trouvé</p>
            {onCancel && (
              <Button variant="light" onPress={onCancel} className="mt-4">
                Continuer sans formulaire
              </Button>
            )}
          </div>
        </CardBody>
      </Card>
    )
  }

  if (questions.length === 0) {
    return (
      <Card className={className}>
        <CardBody className="flex items-center justify-center p-8">
          <div className="text-center">
            <p className="text-default-500">Ce formulaire ne contient aucune question</p>
            {onCancel && (
              <Button variant="light" onPress={onCancel} className="mt-4">
                Continuer
              </Button>
            )}
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="w-full">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">{form.title}</h2>
            <span className="text-sm text-default-500">
              {currentQuestionIndex + 1} / {visibleQuestions.length}
            </span>
          </div>

          {form.showProgressBar && (
            <Progress
              value={progress}
              className="w-full"
              color="primary"
              size="sm"
            />
          )}

          {form.description && (
            <p className="text-default-600 mt-3 text-sm">{form.description}</p>
          )}
        </div>
      </CardHeader>

      <Divider />

      <CardBody className="p-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-[200px]"
          >
            {currentQuestion && (
              <FormQuestion
                question={currentQuestion}
                value={responses[currentQuestion.id]}
                onChange={(response) => handleResponseChange(currentQuestion.id, response)}
                error={validationErrors[currentQuestion.id]}
              />
            )}
          </motion.div>
        </AnimatePresence>

        <div className="flex items-center justify-between mt-8">
          <Button
            variant="light"
            onPress={handlePrevious}
            isDisabled={currentQuestionIndex === 0 || isSubmitting}
            startContent={<ChevronLeft className="size-4" />}
          >
            Précédent
          </Button>

          <div className="flex gap-2">
            {onCancel && (
              <Button
                variant="light"
                onPress={onCancel}
                isDisabled={isSubmitting}
              >
                Annuler
              </Button>
            )}

            <Button
              color="primary"
              onPress={handleNext}
              isLoading={isSubmitting}
              isDisabled={isSubmitting}
              endContent={
                isLastQuestion ? (
                  <Check className="size-4" />
                ) : (
                  <ChevronRight className="size-4" />
                )
              }
            >
              {isLastQuestion ? "Soumettre" : "Suivant"}
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  )
}
